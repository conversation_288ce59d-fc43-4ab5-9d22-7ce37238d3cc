/// Adaptive Quantum RSI - 根据价格变动速度自适应调整的量子RSI
// 基于原始 Quantum RSI 的改进版本，增加了价格变动速度自适应机制
// © Enhanced by AI Assistant

//@version=6
indicator("Adaptive Quantum RSI", overlay=false)

//-------------------------------------------------------------
// 输入参数
//-------------------------------------------------------------
base_length = input.int(14, minval=5, maxval=50, title="基础长度")
maLength = input.int(5, minval=1, title="RSI MA 长度")
maType = input.string("EMA", title="MA 类型", options=["SMA", "EMA", "WMA", "SMMA (RMA)", "VWMA"])
src = input.source(close, title="数据源")

// 自适应参数
velocity_period = input.int(20, minval=10, maxval=50, title="价格速度计算周期")
adaptation_factor = input.float(0.5, minval=0.1, maxval=2.0, step=0.1, title="自适应因子")
min_length = input.int(8, minval=3, maxval=20, title="最小长度")
max_length = input.int(25, minval=15, maxval=50, title="最大长度")

// 显示选项
show_velocity = input.bool(false, title="显示价格速度")
show_adaptive_length = input.bool(false, title="显示自适应长度")
show_threshold_debug = input.bool(false, title="显示阈值调试信息")

// 价格波动过滤参数
min_price_change = input.float(0.2, minval=0.05, maxval=1.0, step=0.05, title="最小价格波动率(%)", tooltip="只有当价格波动大于此值时才触发信号")
price_change_period = input.int(5, minval=1, maxval=20, title="价格波动计算周期", tooltip="计算价格波动的K线数量")

//-------------------------------------------------------------
// 价格变动速度计算
//-------------------------------------------------------------
// 计算价格变化率
price_change = math.abs(src - src[1]) / src[1] * 100
// 计算平均价格变动速度
avg_velocity = ta.sma(price_change, velocity_period)
// 标准化速度（0-1范围）
max_velocity = ta.highest(avg_velocity, velocity_period * 2)
normalized_velocity = max_velocity > 0 ? avg_velocity / max_velocity : 0

// 计算波动率调整因子
volatility = ta.stdev(src, velocity_period) / ta.sma(src, velocity_period) * 100
// 改进波动率标准化，使其更敏感
normalized_volatility = math.min(volatility / 2, 1.0) // 改为除以2，增加敏感度

//-------------------------------------------------------------
// 自适应长度计算
//-------------------------------------------------------------
// 根据价格速度和波动率动态调整长度
speed_adjustment = normalized_velocity * adaptation_factor
length_volatility_adjustment = normalized_volatility * adaptation_factor

// 高速度时缩短长度，低速度时延长长度
adaptive_length = base_length * (1 - speed_adjustment + length_volatility_adjustment)
adaptive_length := math.max(math.min(adaptive_length, max_length), min_length)

//-------------------------------------------------------------
// 增强的高斯衰减函数
//-------------------------------------------------------------
enhanced_decay(n, len, velocity_factor) =>
    // 基础高斯衰减
    base_decay = math.exp(-math.pow(n / len, 2))
    // 速度调整：高速度时衰减更快，低速度时衰减更慢
    velocity_modifier = 1 + velocity_factor * 0.5
    math.pow(base_decay, velocity_modifier)

//-------------------------------------------------------------
// 自适应量子RSI计算
//-------------------------------------------------------------
float g = 0.0
float l = 0.0
int actual_length = math.round(adaptive_length)

for i = 1 to actual_length
    if i <= bar_index
        diff = src[i - 1] - src[i]
        weight = enhanced_decay(i, actual_length, normalized_velocity)
        g += diff > 0 ? diff * weight : 0
        l += diff < 0 ? -diff * weight : 0

// 动量和能量计算
net_momentum = g - l
total_energy = g + l

// 自适应波比率计算（考虑市场状态）
market_state = normalized_velocity > 0.7 ? "高速" : normalized_velocity > 0.3 ? "中速" : "低速"
wave_ratio = total_energy != 0 ? net_momentum / total_energy : 0

// 根据市场状态调整敏感度
sensitivity_multiplier = market_state == "高速" ? 1.2 : market_state == "中速" ? 1.0 : 0.8
adjusted_wave_ratio = wave_ratio * sensitivity_multiplier

// 计算自适应量子RSI
aqrsi = 50 + 50 * adjusted_wave_ratio
// 使用固定长度的EMA来避免series int问题
aqrsi_smoothed = ta.ema(aqrsi, maLength)

//-------------------------------------------------------------
// 动态阈值计算
//-------------------------------------------------------------
// 改进的动态阈值计算
// 基础阈值调整幅度
base_threshold_range = input.float(20.0, minval=5.0, maxval=40.0, step=1.0, title="阈值调整范围")

// 根据波动率和价格速度动态调整超买超卖线
volatility_adjustment = normalized_volatility * base_threshold_range
velocity_adjustment = normalized_velocity * (base_threshold_range * 0.5)

// 综合调整因子
total_adjustment = (volatility_adjustment + velocity_adjustment) / 2

dynamic_overbought = 70 + total_adjustment
dynamic_oversold = 30 - total_adjustment

// 确保阈值在合理范围内，但给予更大的变化空间
dynamic_overbought := math.min(dynamic_overbought, 90)
dynamic_oversold := math.max(dynamic_oversold, 10)

//-------------------------------------------------------------
// 移动平均计算
//-------------------------------------------------------------
ma(source, length, MAtype) =>
    switch MAtype
        "SMA"       => ta.sma(source, length)
        "EMA"       => ta.ema(source, length)
        "WMA"       => ta.wma(source, length)
        "SMMA (RMA)"=> ta.rma(source, length)
        "VWMA"      => ta.vwma(source, length)

aqrsi_ma = ma(aqrsi_smoothed, maLength, maType)

//-------------------------------------------------------------
// 绘图
//-------------------------------------------------------------
// 主要指标线
plot(aqrsi_smoothed, title="Adaptive Quantum RSI", color=color.rgb(212, 12, 202, 11), linewidth=2)
plot(aqrsi_ma, title="AQRSI MA", color=color.rgb(58, 255, 163), linewidth=1)

// 动态阈值线
plot(dynamic_overbought, title="动态超买线", color=color.red, linewidth=1, style=plot.style_line)
plot(dynamic_oversold, title="动态超卖线", color=color.green, linewidth=1, style=plot.style_line)

// 静态参考线
hline(50, "中线", color=color.gray, linestyle=hline.style_dotted)

// 填充区域
plotOver = plot(aqrsi_smoothed > dynamic_overbought ? aqrsi_smoothed : na, title="超买区", color=color.new(color.red, 100))
plotUnder = plot(aqrsi_smoothed < dynamic_oversold ? aqrsi_smoothed : na, title="超卖区", color=color.new(color.green, 100))

// 渐变填充
overbought_line = plot(dynamic_overbought, color=color.new(color.red, 100))
oversold_line = plot(dynamic_oversold, color=color.new(color.green, 100))
fill(plotOver, overbought_line, color=color.new(color.red, 80))
fill(plotUnder, oversold_line, color=color.new(color.green, 80))

//-------------------------------------------------------------
// 辅助信息显示
//-------------------------------------------------------------
plot(show_velocity ? normalized_velocity * 100 : na, title="标准化速度", color=color.yellow, display=display.data_window)
plot(show_adaptive_length ? adaptive_length : na, title="自适应长度", color=color.orange, display=display.data_window)

// 阈值调试信息
plot(show_threshold_debug ? total_adjustment : na, title="阈值调整幅度", color=color.purple, display=display.data_window)
plot(show_threshold_debug ? volatility_adjustment : na, title="波动率调整", color=color.blue, display=display.data_window)
plot(show_threshold_debug ? velocity_adjustment : na, title="速度调整", color=color.aqua, display=display.data_window)
plot(show_threshold_debug ? current_price_volatility : na, title="当前价格波动率", color=color.fuchsia, display=display.data_window)

//-------------------------------------------------------------
// 价格波动过滤器
//-------------------------------------------------------------
// 计算指定周期内的最大价格波动
calc_price_volatility(period) =>
    highest_price = ta.highest(high, period)
    lowest_price = ta.lowest(low, period)
    current_price = close
    // 计算从最高点和最低点到当前价格的波动率
    volatility_from_high = math.abs(current_price - highest_price) / highest_price * 100
    volatility_from_low = math.abs(current_price - lowest_price) / lowest_price * 100
    // 返回最大波动率
    math.max(volatility_from_high, volatility_from_low)

// 计算当前价格波动率
current_price_volatility = calc_price_volatility(price_change_period)

// 价格波动过滤条件
price_volatility_filter = current_price_volatility >= min_price_change

//-------------------------------------------------------------
// 信号生成
//-------------------------------------------------------------
// 基础交叉信号（不含过滤器）
base_bullish_cross = ta.crossover(aqrsi_smoothed, aqrsi_ma) and aqrsi_smoothed < dynamic_oversold
base_bearish_cross = ta.crossunder(aqrsi_smoothed, aqrsi_ma) and aqrsi_smoothed > dynamic_overbought

// 基础极值反转信号（不含过滤器）
base_oversold_reversal = aqrsi_smoothed < dynamic_oversold and aqrsi_smoothed > aqrsi_smoothed[1]
base_overbought_reversal = aqrsi_smoothed > dynamic_overbought and aqrsi_smoothed < aqrsi_smoothed[1]

// 应用价格波动过滤器的最终信号
bullish_cross = base_bullish_cross and price_volatility_filter
bearish_cross = base_bearish_cross and price_volatility_filter
oversold_reversal = base_oversold_reversal and price_volatility_filter
overbought_reversal = base_overbought_reversal and price_volatility_filter

// 绘制有效信号（通过价格波动过滤器）
plotshape(bullish_cross, title="看涨交叉", style=shape.triangleup, location=location.bottom, color=color.green, size=size.small)
plotshape(bearish_cross, title="看跌交叉", style=shape.triangledown, location=location.top, color=color.red, size=size.small)
plotshape(oversold_reversal, title="超卖反转", style=shape.circle, location=location.bottom, color=color.lime, size=size.tiny)
plotshape(overbought_reversal, title="超买反转", style=shape.circle, location=location.top, color=color.orange, size=size.tiny)

// 绘制被过滤的信号（价格波动不足）
plotshape(base_bullish_cross and not price_volatility_filter, title="被过滤的看涨信号",
          style=shape.triangleup, location=location.bottom, color=color.new(color.gray, 50), size=size.tiny)
plotshape(base_bearish_cross and not price_volatility_filter, title="被过滤的看跌信号",
          style=shape.triangledown, location=location.top, color=color.new(color.gray, 50), size=size.tiny)
plotshape(base_oversold_reversal and not price_volatility_filter, title="被过滤的超卖反转",
          style=shape.xcross, location=location.bottom, color=color.new(color.gray, 30), size=size.tiny)
plotshape(base_overbought_reversal and not price_volatility_filter, title="被过滤的超买反转",
          style=shape.xcross, location=location.top, color=color.new(color.gray, 30), size=size.tiny)

//-------------------------------------------------------------
// 表格显示市场状态
//-------------------------------------------------------------
var table info_table = table.new(position.top_right, 2, 8, bgcolor=color.white, border_width=1)

if barstate.islast
    table.cell(info_table, 0, 0, "市场状态", text_color=color.black, bgcolor=color.gray)
    table.cell(info_table, 1, 0, market_state, text_color=color.black)
    table.cell(info_table, 0, 1, "当前长度", text_color=color.black, bgcolor=color.gray)
    table.cell(info_table, 1, 1, str.tostring(math.round(adaptive_length)), text_color=color.black)
    table.cell(info_table, 0, 2, "价格速度", text_color=color.black, bgcolor=color.gray)
    table.cell(info_table, 1, 2, str.tostring(math.round(normalized_velocity * 100)) + "%", text_color=color.black)
    table.cell(info_table, 0, 3, "波动率", text_color=color.black, bgcolor=color.gray)
    table.cell(info_table, 1, 3, str.tostring(math.round(normalized_volatility * 100)) + "%", text_color=color.black)
    table.cell(info_table, 0, 4, "超买线", text_color=color.black, bgcolor=color.gray)
    table.cell(info_table, 1, 4, str.tostring(math.round(dynamic_overbought * 100) / 100), text_color=color.black)
    table.cell(info_table, 0, 5, "超卖线", text_color=color.black, bgcolor=color.gray)
    table.cell(info_table, 1, 5, str.tostring(math.round(dynamic_oversold * 100) / 100), text_color=color.black)
    table.cell(info_table, 0, 6, "价格波动", text_color=color.black, bgcolor=color.gray)
    table.cell(info_table, 1, 6, str.tostring(math.round(current_price_volatility * 100) / 100) + "%",
               text_color=price_volatility_filter ? color.green : color.red)
    table.cell(info_table, 0, 7, "信号状态", text_color=color.black, bgcolor=color.gray)
    signal_status = price_volatility_filter ? "可触发" : "已过滤"
    table.cell(info_table, 1, 7, signal_status,
               text_color=price_volatility_filter ? color.green : color.red)

//-------------------------------------------------------------
// 警报
//-------------------------------------------------------------
if bullish_cross
    alert("AQRSI 看涨交叉信号", alert.freq_once_per_bar)
if bearish_cross
    alert("AQRSI 看跌交叉信号", alert.freq_once_per_bar)
if oversold_reversal
    alert("AQRSI 超卖反转信号", alert.freq_once_per_bar)
if overbought_reversal
    alert("AQRSI 超买反转信号", alert.freq_once_per_bar)
